{% extends 'base.html' %}
{% load static %}

{% block header %}
Workorder
{% endblock %}

{% block extra_head %}
{{ block.super }}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet"
    href="https://cdn.jsdelivr.net/gh/erimicel/select2-tailwindcss-theme/dist/select2-tailwindcss-theme-plain.min.css">
<link href="{% static 'workorders/css/select2-theme.css' %}" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="{% static 'workorders/js/navirec-integration.js' %}"></script>

<!-- Chart.js dependencies for weather history charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
{% endblock %}

{% block content %}
<!-- Workorder section -->
<div class="bg-white shadow-sm rounded-lg mb-4 p-4">
    <form id="workorder-selection-form" class="w-full">
        {% include 'workorders/workorder_filters.html' %}
    </form>
</div>

<div class="grid grid-cols-1 lg:grid-cols-12 gap-4 min-h-screen">
    <div class="lg:col-span-3 flex flex-col h-full">
        {% include 'workorders/workorder_list.html' %}
    </div>

    {% if chart or metadata %}
    <!-- Workorder details sections (when a workorder is selected) -->
    <div class="lg:col-span-6 flex flex-col h-full">
        {% include 'workorders/chart_component.html' %}
    </div>
    <div class="lg:col-span-3 flex flex-col h-full">
        {% include 'workorders/metadata_component.html' %}
    </div>
    {% else %}
    <!-- Unified summary section (when no workorder is selected) -->
    <div class="lg:col-span-9 flex flex-col h-full">
        {% include 'workorders/workorder_summary.html' %}
    </div>
    {% endif %}
</div>

{% if workorder_id %}
<script>
    // Pass workorder_id to JavaScript for Navirec API integration
    window.workorder_id = {{ workorder_id }};
</script>
{% endif %}

{% endblock %}