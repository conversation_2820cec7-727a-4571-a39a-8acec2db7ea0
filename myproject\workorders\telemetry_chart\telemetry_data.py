import time
import logging
import gc
from datetime import datetime, timed<PERSON><PERSON>
from zoneinfo import ZoneInfo
import json
from contextlib import contextmanager
from functools import wraps

import sqlalchemy as sa
from sqlalchemy.orm import Session
import pandas as pd
from google.cloud import bigquery
from django.utils.safestring import mark_safe

from myproject.utils import get_telemetry_db_engine, get_bigquery_client
from ..constants import PROJECT_FIELD_ACTIVITY_TABLE
from ..workorder_data import get_set_code_and_timezone, get_workorder_date_range

logger = logging.getLogger(__name__)


# Utility decorators and context managers
def handle_telemetry_errors(default_return=None):
    """Decorator for consistent error handling across telemetry functions."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                return default_return if default_return is not None else (pd.DataFrame(), {})
        return wrapper
    return decorator


@contextmanager
def telemetry_db_session():
    """Context manager for telemetry database sessions."""
    engine = get_telemetry_db_engine()
    if engine is None:
        raise RuntimeError("Failed to create telemetry database engine")
    
    with Session(bind=engine) as session:
        yield session


# Utility functions
def _generate_status_icon(status, true_class="green", false_class="red"):
    """Generate SVG icons for boolean status display."""
    if status is True:
        return mark_safe(f'<div class="inline-flex justify-center items-center w-6 h-6 bg-{true_class}-100 rounded"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-{true_class}-600"><path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" /></svg></div>')
    elif status is False:
        return mark_safe(f'<div class="inline-flex justify-center items-center w-6 h-6 bg-{false_class}-100 rounded"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-{false_class}-600"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" /></svg></div>')
    return '-'


def _setup_date_range(start_date, end_date, timezone_name=None):
    """Set up the date range for queries, with proper timezone handling."""
    utc = ZoneInfo("UTC")

    # Default end_date to start_date + 1 day if not provided
    if not end_date:
        end_date = start_date + timedelta(days=1)

    # Make sure start_date has time set to beginning of day
    if isinstance(start_date, datetime):
        start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)

    # Make sure end_date has time set to end of day
    if isinstance(end_date, datetime):
        end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)

    # Convert dates to UTC for the query if timezone is provided
    if timezone_name:
        start_utc = start_date.replace(tzinfo=ZoneInfo(timezone_name)).astimezone(utc)
        end_utc = end_date.replace(tzinfo=ZoneInfo(timezone_name)).astimezone(utc)
    else:
        # Assume dates are already in UTC
        start_utc = start_date.replace(tzinfo=utc)
        end_utc = end_date.replace(tzinfo=utc)

    return start_utc, end_utc


def _build_date_range_condition():
    """Build standard date range condition for queries."""
    return """
    AND (
        (intervals.start BETWEEN :start AND :finish)
        OR (intervals.finish BETWEEN :start AND :finish)
        OR (intervals.start <= :start AND intervals.finish >= :finish)
    )
    """


def _get_active_state(data, property_mappings):
    """Helper function to get active state display from boolean properties."""
    for prop_key, display_name in property_mappings.items():
        if data.get(prop_key) is True:
            return display_name
    return ''


# Core data retrieval functions
@handle_telemetry_errors(default_return=([], {}))
def get_tasks_data():
    """Retrieves task data from the telemetry database."""
    with telemetry_db_session() as session:
        tasks_query_text = """
            SELECT code, description
            FROM tasks
            ORDER BY order_value
        """
        result = session.execute(sa.text(tasks_query_text))
        tasks_titles = [title for _, title in result]
        category_orders = {"task_description": tasks_titles}
        return tasks_titles, category_orders


@handle_telemetry_errors()
def get_set_id_from_code(set_code):
    """Get the set_id corresponding to a set_code from the telemetry database."""
    engine = get_telemetry_db_engine()
    if engine is None:
        return None

    query_text = "SELECT set_id FROM sets WHERE code = :set_code LIMIT 1"
    query = sa.text(query_text).bindparams(set_code=set_code)
    
    with engine.connect() as connection:
        result = connection.execute(query).fetchone()
        if result:
            return result[0]
        return None


@handle_telemetry_errors(default_return=[])
def check_available_sets_with_telemetry(date, end_date=None):
    """Checks which sets have telemetry data available for a specific date range."""
    engine = get_telemetry_db_engine()
    if engine is None:
        return []

    start_utc, end_utc = _setup_date_range(date, end_date)

    query_text = f"""
    SELECT DISTINCT sets.code as set_code
    FROM intervals
    INNER JOIN sets ON intervals.set_id = sets.set_id
    WHERE intervals.deleted IS NULL
    {_build_date_range_condition()}
    ORDER BY sets.code
    """

    query = sa.text(query_text).bindparams(start=start_utc, finish=end_utc)
    df = pd.read_sql_query(query, engine)
    return df['set_code'].tolist() if not df.empty else []


# Preset processing helper functions
def _process_preset_item_data(item_key, item_value, timestamp, preset_hash, item_type, preset_type='Applying'):
    """Generic processor for stage/height data."""
    base_record = {
        'timestamp': timestamp,
        'hash': preset_hash,
        'group_key': f"{preset_hash}_{preset_type.lower()}",
        item_type: item_key
    }
    
    if item_type == 'stage':
        return {**base_record, **_process_stage_specific(item_value)}
    elif item_type == 'height':
        return {**base_record, **_process_height_specific(item_value)}
    return base_record


def _process_stage_specific(stage_value):
    """Process stage-specific data."""
    force_axis_state = _get_active_state(stage_value, {
        'do_nothing': 'Do Nothing', 'go_max': 'Go Max', 'go_min': 'Go Min'
    })
    
    main_piston_state = _get_active_state(stage_value, {
        'main_extend': 'Extend', 'main_retract': 'Retract',
        'main_release': 'Release', 'main_do_nothing': 'Do Nothing'
    })

    return {
        'torque': stage_value.get('torque', ''),
        'pitch': stage_value.get('pitch', ''),
        'balancer': _generate_status_icon(stage_value.get('balancer_on')),
        'main_piston_pressure': stage_value.get('main_piston_pressure', ''),
        'main_piston_state': main_piston_state,
        'force_axis': force_axis_state
    }


def _process_height_specific(height_value):
    """Process height-specific data."""
    return {
        'active': _generate_status_icon(height_value.get('active')),
        'height_value': height_value.get('height', ''),
        'side_piston_pressure': height_value.get('side_piston_pressure', '')
    }


def _process_metadata(preset_data, timestamp, preset_hash, preset_type, initialization_name=None):
    """Helper function to process metadata for a preset and return a record."""
    metadata_info = preset_data.get('metadata', {})
    
    # Accordion header name: use initialization_name for applying presets, preset_name for initializing
    if initialization_name:
        accordion_name = initialization_name
    else:
        accordion_name = "__NULL_NAME__"
    
    # Metadata tab name: always use the preset's actual metadata name
    if metadata_info.get('name'):
        metadata_name = metadata_info.get('name')
    else:
        metadata_name = "__NULL_NAME__"
    
    return {
        'timestamp': timestamp,
        'hash': preset_hash,
        'group_key': f"{preset_hash}_{preset_type.lower()}",
        'type': preset_type,
        'name': accordion_name,  # For accordion header
        'metadata_name': metadata_name,  # For metadata tab
        'blade': metadata_info.get('blade', ''),
        'extra': metadata_info.get('extra', ''),
        'material': metadata_info.get('material', '')
    }


def _fetch_preset_intervals(set_id, start_utc, end_utc, timezone_name):
    """Fetch intervals with preset data and convert to set's timezone using SQL."""
    start_time = time.time()
    engine = get_telemetry_db_engine()
    if engine is None:
        return pd.DataFrame()

    query_text = f"""
    SELECT i.interval_id, i.set_id, 
           i.start at time zone 'Zulu' at time zone :timezone as preset_time, 
           sp.hash,
           sp.data as preset_data, im.data as metadata,
           pi.name as initialization_name,
           i.start as interval_start, i.finish as interval_finish
    FROM intervals i
    INNER JOIN interval_metadata im ON i.interval_id = im.interval_id
    INNER JOIN spatula_presets sp ON sp.hash = (im.data::json->'preset'->>'sha256')
    LEFT JOIN preset_initializations pi ON sp.preset_id = pi.preset_id
    WHERE i.set_id = :set_id AND i.deleted IS NULL
          AND im.data::json->'preset'->>'sha256' IS NOT NULL
          AND i.start BETWEEN :start AND :finish
    ORDER BY i.start
    """

    try:
        query = sa.text(query_text).bindparams(set_id=set_id, start=start_utc, finish=end_utc, timezone=timezone_name)
        df = pd.read_sql_query(query, engine)
        
        # SQL already converted timezone - no additional conversion needed
        return df
        
    except Exception as e:
        return pd.DataFrame()


def _categorize_preset_data(df):
    """Process applying presets and configurations with memory optimization."""
    start_time = time.time()
    applying_data = []
    preset_details_data = {'stages': [], 'heights': [], 'metadata': []}
    processed_preset_configs = {}

    applying_count = 0
    error_count = 0

    # Memory optimization: Process in smaller chunks if DataFrame is large
    chunk_size = 1000
    total_rows = len(df)

    if total_rows > chunk_size:
        logger.info(f"Processing {total_rows} preset records in chunks of {chunk_size}")

    for idx, row in df.iterrows():
        try:
            preset_data = row['preset_data']
            if isinstance(preset_data, str):
                try:
                    preset_data = json.loads(preset_data)
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON for preset at index {idx}: {str(e)}")
                    error_count += 1
                    continue

            # Only process presets with groups (group_0, group_1, etc.) - these are applying presets
            has_groups = any(key.startswith('group_') for key in preset_data.keys())

            if has_groups:
                record = {
                    'timestamp': row['preset_time'],
                    'hash': row['hash'],
                    'data': preset_data
                }
                
                applying_data.append(record)
                preset_type = 'Applying'
                applying_count += 1
                
                # Process stages from group_0 and heights from group_1
                if row['hash'] not in processed_preset_configs:
                    _process_grouped_preset_data(preset_data, row, preset_details_data)
                
                # Add metadata if not processed yet
                if row['hash'] not in processed_preset_configs:
                    processed_preset_configs[row['hash']] = True
                    # Check for duplicates before adding metadata
                    group_key = f"{row['hash']}_applying"
                    metadata_exists = any(
                        meta.get('group_key') == group_key
                        for meta in preset_details_data['metadata']
                    )
                    if not metadata_exists:
                        metadata_record = _process_metadata(preset_data, row['preset_time'], row['hash'], preset_type, row['initialization_name'])
                        preset_details_data['metadata'].append(metadata_record)

        except (json.JSONDecodeError, TypeError, KeyError) as e:
            error_count += 1
            continue

    return applying_data, preset_details_data


def _process_grouped_preset_data(preset_data, row, preset_details_data, preset_type='Applying'):
    """Process preset data with groups (group_0, group_1)."""
    stages_processed = 0
    heights_processed = 0
    
    # Create unique keys to prevent duplicates
    preset_hash = row['hash']
    group_key = f"{preset_hash}_{preset_type.lower()}"
    
    # Process stages from group_0
    if 'group_0' in preset_data and isinstance(preset_data['group_0'], dict):
        for key, value in preset_data['group_0'].items():
            if key.startswith('stage_') and isinstance(value, dict):
                # Check if this stage already exists for this group_key
                stage_exists = any(
                    stage.get('group_key') == group_key and stage.get('stage') == key
                    for stage in preset_details_data['stages']
                )
                if not stage_exists:
                    stage_record = _process_preset_item_data(key, value, row['preset_time'], row['hash'], 'stage', preset_type)
                    preset_details_data['stages'].append(stage_record)
                    stages_processed += 1

    # Process heights from group_1
    if 'group_1' in preset_data and isinstance(preset_data['group_1'], dict):
        for key, value in preset_data['group_1'].items():
            if key.startswith('height_') and isinstance(value, dict):
                # Check if this height already exists for this group_key
                height_exists = any(
                    height.get('group_key') == group_key and height.get('height') == key
                    for height in preset_details_data['heights']
                )
                if not height_exists:
                    height_record = _process_preset_item_data(key, value, row['preset_time'], row['hash'], 'height', preset_type)
                    preset_details_data['heights'].append(height_record)
                    heights_processed += 1




# Main data retrieval functions
@handle_telemetry_errors(default_return=(pd.DataFrame(), {}))
def get_preset_applications_data(set_id, timezone_name, start_date, end_date=None):
    """Retrieves preset applications data for a specific set ID and date range."""
    start_time = time.time()
    start_utc, end_utc = _setup_date_range(start_date, end_date, timezone_name)
    df = _fetch_preset_intervals(set_id, start_utc, end_utc, timezone_name)
    
    if df.empty:
        return pd.DataFrame(), {}

    applying_data, preset_details_data = _categorize_preset_data(df)

    applying_df = pd.DataFrame(applying_data)

    # Clean up intermediate data and force garbage collection
    del applying_data
    del df
    gc.collect()

    return applying_df, preset_details_data


@handle_telemetry_errors(default_return=(pd.DataFrame(), {}))
def get_preset_initializations_data(set_id, timezone_name, start_date, end_date=None):
    """Retrieves preset initializations data for a specific set ID and date range."""
    start_time = time.time()
    start_utc, end_utc = _setup_date_range(start_date, end_date, timezone_name)
    
    engine = get_telemetry_db_engine()
    if engine is None:
        return pd.DataFrame(), {}

    # Query to fetch preset initializations with spatula_presets data
    query_text = f"""
    SELECT pi.record_id, pi.set_id, pi.preset_id, pi.name as preset_name,
           pi.timestamp at time zone 'Zulu' at time zone :timezone as preset_time,
           sp.hash, sp.data as preset_data, sp.name as spatula_name
    FROM preset_initializations pi
    INNER JOIN spatula_presets sp ON pi.preset_id = sp.preset_id
    WHERE pi.set_id = :set_id AND pi.deleted IS NULL
          AND sp.deleted IS NULL
          AND pi.timestamp BETWEEN :start AND :finish
    ORDER BY pi.timestamp, pi.record_id
    """

    try:
        query = sa.text(query_text).bindparams(
            set_id=set_id, start=start_utc, finish=end_utc, timezone=timezone_name
        )
        df = pd.read_sql_query(query, engine)
        
        if df.empty:
            return pd.DataFrame(), {}

        # Group initializations by timestamp
        initializing_data, preset_details_data = _process_initializations_data(df)
        
        initializing_df = pd.DataFrame(initializing_data)
        
        return initializing_df, preset_details_data
        
    except Exception as e:
        return pd.DataFrame(), {}


def _process_initializations_data(df):
    """Process preset initializations data with simplified grouping."""
    initializing_data = []
    preset_details_data = {'stages': [], 'heights': [], 'metadata': []}
    processed_records = set()  # Track processed record_ids to avoid duplicates
    
    # Round timestamps to seconds and group by that for chart markers
    df['preset_time_rounded'] = df['preset_time'].dt.round('s')
    grouped = df.groupby('preset_time_rounded')
    
    # Create chart markers (one per timestamp)
    for rounded_timestamp, group in grouped:
        group_hashes = group['hash'].tolist()
        group_names = group['preset_name'].tolist()
        
        # Use first hash as the main identifier for the chart marker
        main_hash = group_hashes[0] if group_hashes else ''
        
        chart_record = {
            'timestamp': rounded_timestamp,  # Use rounded timestamp for chart marker
            'hash': main_hash,  # Used for chart marker identification
            'count': len(group),  # Number of presets initialized at this time
            'hashes': group_hashes,  # All hashes for this timestamp
            'names': group_names   # All preset names for this timestamp
        }
        
        initializing_data.append(chart_record)
    
    # Process each preset individually for accordions (like applying presets)
    for idx, row in df.iterrows():
        # Skip if we've already processed this record
        record_id = row['record_id']
        if record_id in processed_records:
            continue
        processed_records.add(record_id)
        
        try:
            preset_data = row['preset_data']
            if isinstance(preset_data, str):
                preset_data = json.loads(preset_data)
            
            # Use timestamp-based group_key to group all presets from the same initialization event
            preset_hash = row['hash']
            preset_name = row['preset_name']
            rounded_timestamp = row['preset_time'].round('s')
            # Use rounded timestamp to group all presets initialized at the same time
            timestamp_key = rounded_timestamp.strftime('%Y%m%d_%H%M%S')
            group_key = f"{timestamp_key}_{preset_hash}_initializing"
            
            # Process stages and heights if present
            _process_grouped_preset_data(preset_data, row, preset_details_data, 'Initializing')
            
            # Update stages and heights with simplified group_key and filter_timestamp
            for stage in preset_details_data['stages']:
                if stage.get('hash') == preset_hash and stage.get('group_key', '').startswith(preset_hash):
                    stage['group_key'] = group_key
                    stage['filter_timestamp'] = rounded_timestamp
            
            for height in preset_details_data['heights']:
                if height.get('hash') == preset_hash and height.get('group_key', '').startswith(preset_hash):
                    height['group_key'] = group_key
                    height['filter_timestamp'] = rounded_timestamp
            
            # Add metadata with filter timestamp for JavaScript filtering
            metadata_exists = any(
                meta.get('group_key') == group_key
                for meta in preset_details_data['metadata']
            )
            if not metadata_exists:
                metadata_record = _process_metadata(preset_data, row['preset_time'], preset_hash, 'Initializing', preset_name)
                metadata_record['group_key'] = group_key
                metadata_record['name'] = preset_name  # Use name from preset_initializations table
                metadata_record['filter_timestamp'] = rounded_timestamp  # Add for JavaScript filtering
                preset_details_data['metadata'].append(metadata_record)
            
        except (json.JSONDecodeError, TypeError, KeyError) as e:
            continue

    return initializing_data, preset_details_data


@handle_telemetry_errors(default_return=(pd.DataFrame(), {}, pd.DataFrame(), {}))
def get_telemetry_data(set_code, timezone_name, start_date, end_date=None):
    """Retrieves telemetry data for a specific set code and date range."""
    engine = get_telemetry_db_engine()
    if engine is None:
        return pd.DataFrame(), {}, pd.DataFrame(), {}

    # Get tasks data for category ordering
    tasks_titles, category_orders = get_tasks_data()

    # Set up the date range
    start_utc, end_utc = _setup_date_range(start_date, end_date, timezone_name)

    # Define the main telemetry query
    query_text = f"""
    SELECT sets.code as set_code, sets.description as set_description, sets.set_id,
           tasks.code as task_code, tasks.description as task_description,
           intervals.start at time zone 'Zulu' at time zone :timezone as start,
           intervals.finish at time zone 'Zulu' at time zone :timezone as finish
    FROM intervals
    INNER JOIN sets ON intervals.set_id = sets.set_id
    INNER JOIN tasks ON intervals.task_id = tasks.task_id
    WHERE sets.code = :set_code AND intervals.deleted IS NULL
    {_build_date_range_condition()}
    ORDER BY tasks.order_value, intervals.start
    """

    query = sa.text(query_text).bindparams(
        set_code=set_code, start=start_utc, finish=end_utc, timezone=timezone_name
    )

    df = pd.read_sql_query(query, engine)

    # Get preset applications and initializations data if we have telemetry data
    applying_df = pd.DataFrame()
    initializing_df = pd.DataFrame()
    preset_details_data = {}
    
    if not df.empty:
        set_id = df['set_id'].iloc[0] if 'set_id' in df.columns else get_set_id_from_code(set_code)
        if set_id:
            applying_df, applying_details = get_preset_applications_data(
                set_id=set_id, timezone_name=timezone_name, start_date=start_date,
                end_date=end_date
            )
            initializing_df, initializing_details = get_preset_initializations_data(
                set_id=set_id, timezone_name=timezone_name, start_date=start_date,
                end_date=end_date
            )
            
            # Merge preset details data from both applying and initializing
            preset_details_data = applying_details.copy()
            if initializing_details:
                # Merge stages, heights, and metadata
                preset_details_data['stages'].extend(initializing_details.get('stages', []))
                preset_details_data['heights'].extend(initializing_details.get('heights', []))
                preset_details_data['metadata'].extend(initializing_details.get('metadata', []))

    return df, category_orders, applying_df, preset_details_data, initializing_df 