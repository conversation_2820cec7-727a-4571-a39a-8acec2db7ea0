from django.urls import path,include
from . import views
from django.conf import settings

urlpatterns = [
    path('', views.workorder_view, name='workorder_view'),
    path('workorder/<int:workorder_id>/', views.workorder_detail, name='workorder_detail'),
    path('workorder/<int:workorder_id>/video-comments/', views.get_video_comments, name='get_video_comments'),
    path('workorder/<int:workorder_id>/video-comments/add/', views.add_video_comment, name='add_video_comment'),
    path('workorder/<int:workorder_id>/video-comments/<int:comment_id>/delete/', views.delete_video_comment, name='delete_video_comment'),
    path('reset/', views.reset_filters, name='reset_filters'),
    path('workorder-data/', views.workorder_data, name='workorder_data'),
    path('workorder-summary-data/', views.workorder_summary_data, name='workorder_summary_data'),
    path('navirec-api/', views.navirec_api_call, name='navirec_api'),
    path('weather-history/<int:workorder_id>/', views.weather_history_api, name='weather_history_api'),
]

if settings.DEBUG:
    import debug_toolbar
    urlpatterns = [
        path('__debug__/', include(debug_toolbar.urls)),
        path("__reload__/", include("django_browser_reload.urls")),
    ] + urlpatterns
