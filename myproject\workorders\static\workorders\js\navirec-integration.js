/**
 * Simple Navirec API Integration
 * Calls Django backend proxy which handles credentials from utils.py
 */

/**
 * Call any Navirec API endpoint through Django backend proxy
 * @param {string} endpoint - API endpoint (e.g., 'vehicles', 'vehicle_timeline')
 * @param {Object} params - Optional query parameters (e.g., {name: 'test', active: true})
 */
async function callNavirecAPI(endpoint = '', params = {}) {
    try {
        // Get CSRF token
        const csrfToken = document.cookie
            .split('; ')
            .find(row => row.startsWith('csrftoken='))
            ?.split('=')[1] || '';

        // Build URL with parameters
        const url = new URL('/workorders/navirec-api/', window.location.origin);
        url.searchParams.set('endpoint', endpoint);
        // Add query parameters
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined && value !== '') {
                url.searchParams.set(key, value);
            }
        });

        console.log(`🔗 Calling Navirec API: ${endpoint || 'base'}`);
        if (Object.keys(params).length > 0) {
            console.log(`📋 Parameters:`, params);
        }

        const response = await fetch(url.toString(), {
            method: 'GET',
            headers: {
                'X-CSRFToken': csrfToken,
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        });

        if (response.ok) {
            const data = await response.json();
            if (data.error) {
                console.error('❌ Navirec API Error:', data.error);
                return null;
            }
            console.log('✅ Navirec API Response:', data);
            return data;
        } else {
            const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
            console.error('❌ Request Failed:', errorData);
            return null;
        }

    } catch (error) {
        console.error('❌ Network Error:', error);
        return null;
    }
}

// Make it globally available
window.callNavirecAPI = callNavirecAPI;

// Example usage:
// callNavirecAPI('vehicles');                                    // No parameters
// callNavirecAPI('vehicles', {name: 'test'});                    // Single parameter
// callNavirecAPI('vehicles', {name: 'LV-1500', active: true});   // Multiple parameters
